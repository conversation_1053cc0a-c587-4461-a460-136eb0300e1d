#ifndef __IAP_H__
#define __IAP_H__
#include "sys.h"  
#include "app_crc_verify.h"  // 添加CRC验证支持

typedef  void (*iapfun)(void);				//����һ���������͵Ĳ���.

#define FLASH_APP1_ADDR		0x8005000  	//��һ��Ӧ�ó�����ʼ��ַ(�����FLASH)
											//����0X08000000~0X08004FFF�Ŀռ�ΪIAPʹ��

void iap_load_app(uint32_t appxaddr);			//ִ��flash�����app����
void iap_load_app_with_crc_check(uint32_t appxaddr);  //ִ��flash�����app����(��CRC����)

void iap_write_appbin(uint32_t appxaddr,uint8_t *appbuf,uint32_t applen);	//��ָ����ַ��ʼ,д��bin
uint8_t iap_write_block(uint32_t flash_addr, uint8_t *data, uint32_t len);     //д�뵥�����ݿ鵽Flash
void iap_erase_app_area(void);               //����APP����Flash(108KB)
#endif







































