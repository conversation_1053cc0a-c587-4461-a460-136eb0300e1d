#ifndef __IAP_H__
#define __IAP_H__
#include "sys.h"  
#include "app_crc_verify.h"  // 添加CRC验证支持

typedef  void (*iapfun)(void);				//定义一个函数类型的参数

#define FLASH_APP1_ADDR		0x8005000  	//第一个应用程序起始地址(存放在FLASH)
											//保留0X08000000~0X08004FFF的空间为IAP使用

void iap_load_app(uint32_t appxaddr);			//执行flash里面的app程序
void iap_load_app_with_crc_check(uint32_t appxaddr);  //执行flash里面的app程序(带CRC检查)

void iap_write_appbin(uint32_t appxaddr,uint8_t *appbuf,uint32_t applen);	//在指定地址开始,写入bin
uint8_t iap_write_block(uint32_t flash_addr, uint8_t *data, uint32_t len);     //写入单个数据块到Flash
void iap_erase_app_area(void);               //擦除APP区域Flash(108KB)
#endif







































