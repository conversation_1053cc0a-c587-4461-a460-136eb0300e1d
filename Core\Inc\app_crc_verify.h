/**
 * @file app_crc_verify.h
 * @brief 应用程序CRC验证功能
 * @date 2025-08-22
 * <AUTHOR>
 */

#ifndef __APP_CRC_VERIFY_H__
#define __APP_CRC_VERIFY_H__

#include <stdint.h>
#include <stdbool.h>
#include "stm32f1xx_hal.h"

/* 应用程序基地址 (与IAP模块保持一致) */
#define APP_BASE_ADDRESS    0x08005000

/* CRC32计算参数 (与Python脚本保持一致) */
#define CRC32_POLYNOMIAL    0x04C11DB7
#define CRC32_INIT_VALUE    0x00000000

/* 应用程序最大大小 (根据Flash布局设定) */
#define MAX_APP_SIZE        0x1B000  // 108KB

/**
 * @brief 计算数据的CRC32值
 * @param data 数据指针
 * @param length 数据长度
 * @return CRC32值
 */
uint32_t calculate_crc32(const uint8_t *data, uint32_t length);

/**
 * @brief 验证应用程序的CRC
 * @param app_start_addr 应用程序起始地址
 * @param app_size 应用程序总大小 (包括CRC的4字节)
 * @return true: 验证通过, false: 验证失败
 */
bool verify_app_crc(uint32_t app_start_addr, uint32_t app_size);

/**
 * @brief 获取应用程序的实际大小
 * @return 应用程序大小 (字节), 0表示无效
 */
uint32_t get_app_size(void);

/**
 * @brief 检查应用程序是否有效（包括CRC验证）
 * @return true: 应用程序有效, false: 应用程序无效
 */
bool is_app_valid(void);

/**
 * @brief 扫描Flash找到实际的应用程序大小
 * @param start_addr 扫描起始地址
 * @param max_size 最大扫描大小
 * @return 实际大小，如果无法确定则返回0
 */
uint32_t scan_app_size(uint32_t start_addr, uint32_t max_size);

#endif /* __APP_CRC_VERIFY_H__ */
