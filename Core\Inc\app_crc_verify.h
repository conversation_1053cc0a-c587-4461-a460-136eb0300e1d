/**
 * @file app_crc_verify.h
 * @brief Application CRC verification functionality
 * @date 2025-08-22
 * <AUTHOR>
 */

#ifndef __APP_CRC_VERIFY_H__
#define __APP_CRC_VERIFY_H__

#include <stdint.h>
#include <stdbool.h>
#include "stm32f1xx_hal.h"

/* Application base address (consistent with IAP module) */
#define APP_BASE_ADDRESS    0x08005000

/* CRC32 calculation parameters (consistent with Python script) */
#define CRC32_POLYNOMIAL    0x04C11DB7
#define CRC32_INIT_VALUE    0x00000000

/* Maximum application size (based on Flash layout) */
#define MAX_APP_SIZE        0x1B000  // 108KB

/**
 * @brief Calculate CRC32 value of data
 * @param data Data pointer
 * @param length Data length
 * @return CRC32 value
 */
uint32_t calculate_crc32(const uint8_t *data, uint32_t length);

/**
 * @brief Verify application CRC
 * @param app_start_addr Application start address
 * @param app_size Total application size (including 4 bytes of CRC)
 * @return true: verification passed, false: verification failed
 */
bool verify_app_crc(uint32_t app_start_addr, uint32_t app_size);

/**
 * @brief Get actual application size
 * @return Application size (bytes), 0 indicates invalid
 */
uint32_t get_app_size(void);

/**
 * @brief Check if application is valid (including CRC verification)
 * @return true: application valid, false: application invalid
 */
bool is_app_valid(void);

/**
 * @brief Scan Flash to find actual application size
 * @param start_addr Scan start address
 * @param max_size Maximum scan size
 * @return Actual size, returns 0 if cannot be determined
 */
uint32_t scan_app_size(uint32_t start_addr, uint32_t max_size);

#endif /* __APP_CRC_VERIFY_H__ */
