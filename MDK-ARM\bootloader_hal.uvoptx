<?xml version="1.0" encoding="UTF-8"?>
<ProjectOpt xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_optx.xsd">
	<SchemaVersion>1.0</SchemaVersion>
	<Header>### uVision Project, (C) Keil Software</Header>
	<Extensions>
		<cExt>*.c</cExt>
		<aExt>*.s*; *.src; *.a*</aExt>
		<oExt>*.obj; *.o</oExt>
		<lExt>*.lib</lExt>
		<tExt>*.txt; *.h; *.inc</tExt>
		<pExt>*.plm</pExt>
		<CppX>*.cpp</CppX>
		<nMigrate>0</nMigrate>
	</Extensions>
	<DaveTm>
		<dwLowDateTime>0</dwLowDateTime>
		<dwHighDateTime>0</dwHighDateTime>
	</DaveTm>
	<Target>
		<TargetName>bootloader_hal</TargetName>
		<ToolsetNumber>0x4</ToolsetNumber>
		<ToolsetName>ARM-ADS</ToolsetName>
		<TargetOption>
			<CLKADS>8000000</CLKADS>
			<OPTTT>
				<gFlags>1</gFlags>
				<BeepAtEnd>1</BeepAtEnd>
				<RunSim>0</RunSim>
				<RunTarget>1</RunTarget>
				<RunAbUc>0</RunAbUc>
			</OPTTT>
			<OPTHX>
				<HexSelection>1</HexSelection>
				<FlashByte>65535</FlashByte>
				<HexRangeLowAddress>0</HexRangeLowAddress>
				<HexRangeHighAddress>0</HexRangeHighAddress>
				<HexOffset>0</HexOffset>
			</OPTHX>
			<OPTLEX>
				<PageWidth>79</PageWidth>
				<PageLength>66</PageLength>
				<TabStop>8</TabStop>
				<ListingPath />
			</OPTLEX>
			<ListingPage>
				<CreateCListing>1</CreateCListing>
				<CreateAListing>1</CreateAListing>
				<CreateLListing>1</CreateLListing>
				<CreateIListing>0</CreateIListing>
				<AsmCond>1</AsmCond>
				<AsmSymb>1</AsmSymb>
				<AsmXref>0</AsmXref>
				<CCond>1</CCond>
				<CCode>0</CCode>
				<CListInc>0</CListInc>
				<CSymb>0</CSymb>
				<LinkerCodeListing>0</LinkerCodeListing>
			</ListingPage>
			<OPTXL>
				<LMap>1</LMap>
				<LComments>1</LComments>
				<LGenerateSymbols>1</LGenerateSymbols>
				<LLibSym>1</LLibSym>
				<LLines>1</LLines>
				<LLocSym>1</LLocSym>
				<LPubSym>1</LPubSym>
				<LXref>0</LXref>
				<LExpSel>0</LExpSel>
			</OPTXL>
			<OPTFL>
				<tvExp>1</tvExp>
				<tvExpOptDlg>0</tvExpOptDlg>
				<IsCurrentTarget>1</IsCurrentTarget>
			</OPTFL>
			<CpuCode>18</CpuCode>
			<DebugOpt>
				<uSim>0</uSim>
				<uTrg>1</uTrg>
				<sLdApp>1</sLdApp>
				<sGomain>1</sGomain>
				<sRbreak>1</sRbreak>
				<sRwatch>1</sRwatch>
				<sRmem>1</sRmem>
				<sRfunc>1</sRfunc>
				<sRbox>1</sRbox>
				<tLdApp>1</tLdApp>
				<tGomain>1</tGomain>
				<tRbreak>1</tRbreak>
				<tRwatch>1</tRwatch>
				<tRmem>1</tRmem>
				<tRfunc>1</tRfunc>
				<tRbox>1</tRbox>
				<tRtrace>1</tRtrace>
				<sRSysVw>1</sRSysVw>
				<tRSysVw>1</tRSysVw>
				<sRunDeb>0</sRunDeb>
				<sLrtime>0</sLrtime>
				<bEvRecOn>1</bEvRecOn>
				<bSchkAxf>0</bSchkAxf>
				<bTchkAxf>0</bTchkAxf>
				<nTsel>6</nTsel>
				<sDll />
				<sDllPa />
				<sDlgDll />
				<sDlgPa />
				<sIfile />
				<tDll />
				<tDllPa />
				<tDlgDll />
				<tDlgPa />
				<tIfile />
				<pMon>STLink\ST-LINKIII-KEIL_SWO.dll</pMon>
			</DebugOpt>
			<TargetDriverDllRegistry>
				<SetRegEntry>
					<Number>0</Number>
					<Key>ST-LINKIII-KEIL_SWO</Key>
					<Name>-U-O142 -O2254 -S0 -C0 -N00("ARM CoreSight SW-DP") -D00(2BA01477) -L00(0) -TO18 -********** -TP21 -TDS8007 -TDT0 -TDC1F -TIEFFFFFFFF -TIP8 -FO7 -********** -FC800 -FN1 -FF0STM32F10x_128 -********** -FL010000 -FP0($$Device:STM32F103C8$Flash\STM32F10x_128.FLM)</Name>
				</SetRegEntry>
				<SetRegEntry>
					<Number>0</Number>
					<Key />
					<Name />
				</SetRegEntry>
			</TargetDriverDllRegistry>
			<Breakpoint />
			<Tracepoint>
				<THDelay>0</THDelay>
			</Tracepoint>
			<DebugFlag>
				<trace>0</trace>
				<periodic>1</periodic>
				<aLwin>1</aLwin>
				<aCover>0</aCover>
				<aSer1>0</aSer1>
				<aSer2>0</aSer2>
				<aPa>0</aPa>
				<viewmode>1</viewmode>
				<vrSel>0</vrSel>
				<aSym>0</aSym>
				<aTbox>0</aTbox>
				<AscS1>0</AscS1>
				<AscS2>0</AscS2>
				<AscS3>0</AscS3>
				<aSer3>0</aSer3>
				<eProf>0</eProf>
				<aLa>0</aLa>
				<aPa1>0</aPa1>
				<AscS4>0</AscS4>
				<aSer4>0</aSer4>
				<StkLoc>1</StkLoc>
				<TrcWin>0</TrcWin>
				<newCpu>0</newCpu>
				<uProt>0</uProt>
			</DebugFlag>
			<LintExecutable />
			<LintConfigFile />
			<bLintAuto>0</bLintAuto>
			<bAutoGenD>0</bAutoGenD>
			<LntExFlags>0</LntExFlags>
			<pMisraName />
			<pszMrule />
			<pSingCmds />
			<pMultCmds />
			<pMisraNamep />
			<pszMrulep />
			<pSingCmdsp />
			<pMultCmdsp />
		</TargetOption>
	</Target>
</ProjectOpt>
