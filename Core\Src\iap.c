#include "sys.h"
// #include "delay.h"
#include "usart.h"
#include "stmflash.h"
#include "iap.h"
#include "app_crc_verify.h"  // 添加CRC验证支持


iapfun jump2app;
uint16_t iapbuf[512];
//appxaddr:
//appbuf:Ӧ�ó���CODE.
//appsize:Ӧ�ó����С(�ֽ�).
void iap_write_appbin ( uint32_t appxaddr, uint8_t* appbuf, uint32_t appsize )
{
    uint16_t t;
    uint16_t i = 0;
    uint16_t temp;
    uint32_t fwaddr = appxaddr; //��ǰд��ĵ�ַ
    uint8_t* dfu = appbuf;
    for ( t = 0; t < appsize; t += 2 )
    {
        temp = ( uint16_t ) dfu[1] << 8;
        temp += ( uint16_t ) dfu[0];
        dfu += 2; //ƫ��2���ֽ�
        iapbuf[i++] = temp;
        if ( i == 512 )
        {
            i = 0;
            STMFLASH_Write ( fwaddr, iapbuf, 512 );
            fwaddr += 1024;	//ƫ��1024
        }
    }
    if ( i ) STMFLASH_Write ( fwaddr, iapbuf, i ); //������һЩ�����ֽ�д��ȥ.
}

//��ת��Ӧ�ó����
//appxaddr:�û�������ʼ��ַ.
void iap_load_app ( uint32_t appxaddr )
{
    if ( ( ( * ( volatile uint32_t* ) appxaddr ) & 0x2FFE0000 ) == 0x20000000 )	//���ջ����ַ�Ƿ�Ϸ�.
    {
        __set_MSP ( * ( volatile uint32_t* ) appxaddr );					//��ʼ��APP��ջָ��(�û��������ĵ�һ�������ڴ��ջ����ַ)
        jump2app = ( iapfun ) * ( volatile uint32_t* ) ( appxaddr + 4 );		//�û��������ڶ�����Ϊ����ʼ��ַ(��λ��ַ)
        MSR_MSP(*(volatile uint32_t*)appxaddr);
        jump2app();									//��ת��APP
    }
}

// д�뵥�����ݿ鵽Flash
uint8_t iap_write_block(uint32_t flash_addr, uint8_t *data, uint32_t len)
{
    HAL_StatusTypeDef status = HAL_OK;
    
    printf("Debug: Writing %d bytes to 0x%08X\r\n", len, flash_addr);
    
    // ����Flash
    HAL_FLASH_Unlock();
    
    // �������д��
    for (uint32_t i = 0; i < len; i += 2)
    {
        uint16_t data_to_write;
        
        if (i + 1 < len) {
            // ��������16λ����
            data_to_write = (uint16_t)data[i + 1] << 8 | (uint16_t)data[i];
        } else {
            // ���һ���ֽڣ���λ��0
            data_to_write = (uint16_t)data[i];
        }
        
        // д��Flash
        status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_HALFWORD, 
                                 flash_addr + i, 
                                 data_to_write);
        
        if (status != HAL_OK) {
            printf("Flash write error at address 0x%08X, status: %d\r\n", 
                   flash_addr + i, status);
            HAL_FLASH_Lock();
            return 1; // д��ʧ��
        }
    }
    
    // ����Flash
    HAL_FLASH_Lock();
    
    printf("Debug: Successfully wrote %d bytes\r\n", len);
    return 0; // д��ɹ�
}

// ����APP����Flash(108KB)
void iap_erase_app_area(void)
{
    FLASH_EraseInitTypeDef EraseInitStruct;
    uint32_t PAGEError = 0;
    HAL_StatusTypeDef status;
    
    printf("Starting Flash erase...\r\n");
    
    // ����Flash
    HAL_FLASH_Unlock();
    
    // ���ò�������
    EraseInitStruct.TypeErase = FLASH_TYPEERASE_PAGES;
    EraseInitStruct.PageAddress = FLASH_APP1_ADDR;  // ��0x08005000��ʼ
    EraseInitStruct.NbPages = 108;  // ����108��ҳ��(108KB)��ÿҳ1KB
    
    // ִ�в���
    status = HAL_FLASHEx_Erase(&EraseInitStruct, &PAGEError);
    
    if (status == HAL_OK) {
        printf("Flash erase completed successfully! Erased 108KB from 0x%08X\r\n", FLASH_APP1_ADDR);
    } else {
        printf("Flash erase failed! Error code: %d, Page error: 0x%08X\r\n", status, PAGEError);
    }
    
    // ����Flash
    HAL_FLASH_Lock();
}

//��ת��Ӧ�ó���� (��CRC����)
void iap_load_app_with_crc_check(uint32_t appxaddr)
{
    printf("��ʼ��������CRC��������...\r\n");
    
    // ����Ӧ�ó����Ƿ���Ч
    if (!is_app_valid()) {
        printf("Ӧ�ó���CRC��֤ʧ�ܣ��޷���ת!\r\n");
        return;
    }
    
    printf("Ӧ�ó���CRC��֤ͨ����ʼ��ת...\r\n");
    
    // CRC��֤ͨ������ִ����ת
    iap_load_app(appxaddr);
}














