#include "sys.h"
#include "usart.h"
#include "stmflash.h"
#include "iap.h"
#include "app_crc_verify.h"  // 添加CRC验证支持
#include "stdio.h"

iapfun jump2app;
uint16_t iapbuf[512];
//appxaddr: 应用程序地址
//appbuf: 应用程序CODE数据
//appsize: 应用程序大小(字节)
void iap_write_appbin ( uint32_t appxaddr, uint8_t* appbuf, uint32_t appsize )
{
    uint16_t t;
    uint16_t i = 0;
    uint16_t temp;
    uint32_t fwaddr = appxaddr; //当前写入的地址
    uint8_t* dfu = appbuf;
    for ( t = 0; t < appsize; t += 2 )
    {
        temp = ( uint16_t ) dfu[1] << 8;
        temp += ( uint16_t ) dfu[0];
        dfu += 2; //偏移2个字节
        iapbuf[i++] = temp;
        if ( i == 512 )
        {
            i = 0;
            STMFLASH_Write ( fwaddr, iapbuf, 512 );
            fwaddr += 1024;	//偏移1024字节
        }
    }
    if ( i ) STMFLASH_Write ( fwaddr, iapbuf, i ); //将剩余的一些数据字节写进去
}

//跳转到应用程序
//appxaddr:用户代码起始地址
void iap_load_app ( uint32_t appxaddr )
{
    if ( ( ( * ( volatile uint32_t* ) appxaddr ) & 0x2FFE0000 ) == 0x20000000 )	//检查栈顶地址是否合法
    {
        __set_MSP ( * ( volatile uint32_t* ) appxaddr );					//初始化APP堆栈指针(用户代码区的第一个字为存放堆栈地址)
        jump2app = ( iapfun ) * ( volatile uint32_t* ) ( appxaddr + 4 );		//用户代码区第二个字为程序开始地址(复位地址)
        MSR_MSP(*(volatile uint32_t*)appxaddr);
        jump2app();									//跳转到APP
    }
}

// 写入单个数据块到Flash
uint8_t iap_write_block(uint32_t flash_addr, uint8_t *data, uint32_t len)
{
    HAL_StatusTypeDef status = HAL_OK;
    
    printf("Debug: Writing %d bytes to 0x%08X\r\n", len, flash_addr);
    
    // 解锁Flash
    HAL_FLASH_Unlock();

    // 按字节写入
    for (uint32_t i = 0; i < len; i += 2)
    {
        uint16_t data_to_write;
        
        if (i + 1 < len) {
            // 组合成16位数据
            data_to_write = (uint16_t)data[i + 1] << 8 | (uint16_t)data[i];
        } else {
            // 最后一个字节，高位补0
            data_to_write = (uint16_t)data[i];
        }

        // 写入Flash
        status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_HALFWORD, 
                                 flash_addr + i, 
                                 data_to_write);
        
        if (status != HAL_OK) {
            printf("Flash write error at address 0x%08X, status: %d\r\n", 
                   flash_addr + i, status);
            HAL_FLASH_Lock();
            return 1; // 写入失败
        }
    }

    // 锁定Flash
    HAL_FLASH_Lock();
    
    printf("Debug: Successfully wrote %d bytes\r\n", len);
    return 0; // 写入成功
}

// 擦除APP区域Flash(108KB)
void iap_erase_app_area(void)
{
    FLASH_EraseInitTypeDef EraseInitStruct;
    uint32_t PAGEError = 0;
    HAL_StatusTypeDef status;
    
    printf("Starting Flash erase...\r\n");
    
    // 解锁Flash
    HAL_FLASH_Unlock();

    // 设置擦除参数
    EraseInitStruct.TypeErase = FLASH_TYPEERASE_PAGES;
    EraseInitStruct.PageAddress = FLASH_APP1_ADDR;  // 从0x08005000开始
    EraseInitStruct.NbPages = 108;  // 擦除108个页面(108KB)，每页1KB

    // 执行擦除
    status = HAL_FLASHEx_Erase(&EraseInitStruct, &PAGEError);
    
    if (status == HAL_OK) {
        printf("Flash erase completed successfully! Erased 108KB from 0x%08X\r\n", FLASH_APP1_ADDR);
    } else {
        printf("Flash erase failed! Error code: %d, Page error: 0x%08X\r\n", status, PAGEError);
    }
    
    // 锁定Flash
    HAL_FLASH_Lock();
}

//跳转到应用程序 (带CRC检查)
void iap_load_app_with_crc_check(uint32_t appxaddr)
{
    printf("Starting jump with CRC check...\r\n");

    // 检查应用程序是否有效
    if (!is_app_valid()) {
        printf("Application CRC verification failed, cannot jump!\r\n");
        return;
    }

    printf("Application CRC verification passed, starting jump...\r\n");

    // CRC验证通过，正常执行跳转
    iap_load_app(appxaddr);
}














