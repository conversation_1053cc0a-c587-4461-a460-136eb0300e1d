/**
 * @file app_crc_verify.c
 * @brief Application CRC verification functionality implementation
 * @date 2025-08-22
 * <AUTHOR>
 */

#include "app_crc_verify.h"
#include "stmflash.h"
#include "usart.h"
#include "stdio.h"

/**
 * @brief Calculate CRC32 value of data
 * @param data Data pointer
 * @param length Data length
 * @return CRC32 value
 *
 * @note Uses same parameters as Python script:
 *       - Polynomial: 0x04C11DB7
 *       - Initial value: 0x00000000
 *       - No input reflection
 *       - No output reflection
 *       - No output XOR
 */
uint32_t calculate_crc32(const uint8_t *data, uint32_t length)
{
    uint32_t crc = CRC32_INIT_VALUE;
    uint32_t i, j;
    
    for (i = 0; i < length; i++) {
        uint8_t byte = data[i];
        
        // Move byte to high bits
        crc ^= (uint32_t)byte << 24;

        // Process each bit
        for (j = 0; j < 8; j++) {
            if (crc & 0x80000000) {
                crc = (crc << 1) ^ CRC32_POLYNOMIAL;
            } else {
                crc = crc << 1;
            }
        }
    }
    
    return crc;
}

/**
 * @brief Verify application CRC
 * @param app_start_addr Application start address
 * @param app_size Total application size (including 4 bytes of CRC)
 * @return true: verification passed, false: verification failed
 */
bool verify_app_crc(uint32_t app_start_addr, uint32_t app_size)
{
    // Parameter check
    if (app_size < 4) {
        printf("CRC verification failed: firmware size too small (%lu bytes)\r\n", app_size);
        return false;
    }

    if (app_size > MAX_APP_SIZE) {
        printf("CRC verification failed: firmware size exceeds limit (%lu > %lu)\r\n", app_size, MAX_APP_SIZE);
        return false;
    }

    // Calculate application data size (total size - 4 bytes of CRC)
    uint32_t app_data_size = app_size - 4;

    printf("Starting CRC verification...\r\n");
    printf("Application address: 0x%08lX\r\n", app_start_addr);
    printf("Application size: %lu bytes\r\n", app_data_size);
    
    // Get CRC value stored at the end (little endian format)
    uint8_t *crc_addr = (uint8_t *)(app_start_addr + app_data_size);
    uint32_t stored_crc = 0;

    // Convert from little endian byte order to 32-bit value
    stored_crc = (uint32_t)crc_addr[0] |
                 ((uint32_t)crc_addr[1] << 8) |
                 ((uint32_t)crc_addr[2] << 16) |
                 ((uint32_t)crc_addr[3] << 24);
    
    printf("Stored CRC: 0x%08lX\r\n", stored_crc);

    // Calculate CRC of application data (excluding CRC value at the end)
    uint32_t calculated_crc = calculate_crc32((uint8_t *)app_start_addr, app_data_size);

    printf("Calculated CRC: 0x%08lX\r\n", calculated_crc);

    // Compare CRC values
    if (stored_crc == calculated_crc) {
        printf("CRC verification passed!\r\n");
        return true;
    } else {
        printf("CRC verification failed! (Stored: 0x%08lX, Calculated: 0x%08lX)\r\n", stored_crc, calculated_crc);
        return false;
    }
}

/**
 * @brief Scan Flash to find actual application size
 * @param start_addr Scan start address
 * @param max_size Maximum scan size
 * @return Actual size, returns 0 if cannot be determined
 */
uint32_t scan_app_size(uint32_t start_addr, uint32_t max_size)
{
    uint32_t size = 0;
    uint8_t *ptr = (uint8_t *)start_addr;

    // Scan backwards to find first non-0xFF position
    for (uint32_t i = max_size; i > 0; i--) {
        if (ptr[i - 1] != 0xFF) {
            size = i;
            break;
        }
    }
    
    // Check if data was found
    if (size == 0) {
        printf("Scan failed: no valid data found\r\n");
        return 0;
    }

    // Ensure size is multiple of 4 (CRC alignment)
    if (size % 4 != 0) {
        size = ((size / 4) + 1) * 4;
    }

    printf("Scanned application size: %lu bytes\r\n", size);
    return size;
}

/**
 * @brief Get actual application size
 * @return Application size (bytes), 0 indicates invalid
 */
uint32_t get_app_size(void)
{
    // Method 1: Use fixed size (known from compilation info)
    // Current application compiled size is 38668 bytes (38664 + 4 CRC)
    uint32_t expected_size = 38668;

    // Verify if this size is reasonable
    if (expected_size <= MAX_APP_SIZE) {
        printf("Using expected application size: %lu bytes\r\n", expected_size);
        return expected_size;
    }

    // Method 2: Scan Flash content to determine size
    printf("Expected size exceeds limit, starting Flash scan...\r\n");
    return scan_app_size(APP_BASE_ADDRESS, MAX_APP_SIZE);
}

/**
 * @brief 检查应用程序是否有效（包括向量表和CRC验证）
 * @return true: 应用程序有效, false: 应用程序无效
 */
bool is_app_valid(void)
{
    printf("==========================================\r\n");
    printf("Starting application integrity check...\r\n");

    // 1. 检查向量表（栈指针）
    uint32_t stack_ptr = *(volatile uint32_t*)APP_BASE_ADDRESS;
    if ((stack_ptr & 0x2FFE0000) != 0x20000000) {
        printf("Vector table check failed: invalid stack pointer 0x%08lX\r\n", stack_ptr);
        return false;
    }
    printf("Vector table check passed: stack pointer 0x%08lX\r\n", stack_ptr);

    // 2. 检查复位向量
    uint32_t reset_vector = *(volatile uint32_t*)(APP_BASE_ADDRESS + 4);
    if ((reset_vector & 0xFF000000) != 0x08000000) {
        printf("Vector table check failed: invalid reset vector 0x%08lX\r\n", reset_vector);
        return false;
    }
    printf("Vector table check passed: reset vector 0x%08lX\r\n", reset_vector);
    
    // 3. 获取应用程序大小
    uint32_t app_size = get_app_size();
    if (app_size == 0 || app_size < 4) {
        printf("Application size check failed: %lu bytes\r\n", app_size);
        return false;
    }

    // 4. 进行CRC验证
    bool crc_valid = verify_app_crc(APP_BASE_ADDRESS, app_size);

    if (crc_valid) {
        printf("Application integrity check passed!\r\n");
        printf("==========================================\r\n");
        return true;
    } else {
        printf("Application integrity check failed!\r\n");
        printf("==========================================\r\n");
        return false;
    }
}

/**
 * @brief 验证固件更新完成后的完整性
 * @param total_size 接收到的固件总大小
 * @return true: 验证通过, false: 验证失败
 */
bool verify_firmware_after_update(uint32_t total_size)
{
    printf("==========================================\r\n");
    printf("Firmware update completed, starting verification...\r\n");
    printf("Received firmware size: %lu bytes\r\n", total_size);

    // 检查大小是否合理
    if (total_size < 4 || total_size > MAX_APP_SIZE) {
        printf("Firmware size unreasonable: %lu bytes\r\n", total_size);
        return false;
    }

    // 进行CRC验证
    bool result = verify_app_crc(APP_BASE_ADDRESS, total_size);

    if (result) {
        printf("Firmware verification successful! Safe to start application.\r\n");
    } else {
        printf("Firmware verification failed! Please re-flash firmware.\r\n");
    }
    
    printf("==========================================\r\n");
    return result;
}
