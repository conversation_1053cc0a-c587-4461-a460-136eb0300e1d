#ifndef __CANOPEN_H
#define __CANOPEN_H

#include "can.h"

#define CAN_REC_LEN  			1024 //定义缓冲区大小 1KB
#define FLASH_WRITE_SIZE 		1024 //每次写入Flash的大小(字节)

// CAN命令定义
#define CMD_CAN_ID              0x101  // 命令帧ID
#define CMD_JUMP_TO_APP         0x01   // 跳转到APP命令
#define CMD_ERASE_FLASH         0x02   // 擦除Flash命令

extern CAN_TxHeaderTypeDef     TxMeg;
extern CAN_RxHeaderTypeDef     RxMeg;

extern uint8_t  CAN_Rx_Data[8];
extern uint8_t  CAN_Tx_Data[8];

extern uint16_t CAN_Baudrate;
extern uint8_t Error_register;

// 流式更新相关变量
extern uint32_t CAN1_RX_CNT; // CAN1接收计数（当前缓冲区索引）
extern uint8_t CAN_RX_BUF_A[CAN_REC_LEN]; // 缓冲区A
extern uint8_t CAN_RX_BUF_B[CAN_REC_LEN]; // 缓冲区B
extern uint8_t *current_rx_buffer;        // 当前接收缓冲区指针
extern uint8_t *current_flash_buffer;     // 当前Flash写入缓冲区指针
extern uint32_t current_flash_addr;       // 当前Flash写入地址
extern uint32_t total_received_bytes;     // 总接收字节数
extern uint8_t flash_write_pending;       // Flash写入挂起标志
extern uint32_t last_receive_time;        // 最后接收时间戳
extern uint8_t jump_to_app_cmd;            // 跳转到APP命令标志
extern uint8_t erase_flash_cmd;            // 擦除Flash命令标志

void CAN_User_Init(CAN_HandleTypeDef* hcan);
void firmware_stream_init(void);           // 流式固件更新初始化
uint8_t check_receive_timeout(void);       // 检查接收超时
void reset_firmware_state(void);           // 重置固件接收状态



#endif
