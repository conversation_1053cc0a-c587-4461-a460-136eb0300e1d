{"name": "bootloader_hal", "target": "bootloader_hal", "toolchain": "AC5", "toolchainLocation": "C:\\Keil_v5\\ARM\\ARMCC", "toolchainCfgFile": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.14\\res\\data\\models/arm.v5.model.json", "buildMode": "fast|multhread", "showRepathOnLog": true, "threadNum": 16, "rootDir": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "dumpPath": "build\\bootloader_hal", "outDir": "build\\bootloader_hal", "ram": 20480, "rom": 20479, "incDirs": ["../Core/Inc", "../Drivers/STM32F1xx_HAL_Driver/Inc", "../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy", "../Drivers/CMSIS/Device/ST/STM32F1xx/Include", "../Drivers/CMSIS/Include", ".cmsis/include", "RTE/_bootloader_hal"], "libDirs": [], "defines": ["USE_HAL_DRIVER", "STM32F103xB"], "sourceList": ["../Core/Src/app_crc_verify.c", "../Core/Src/can.c", "../Core/Src/canopen.c", "../Core/Src/gpio.c", "../Core/Src/iap.c", "../Core/Src/main.c", "../Core/Src/stm32f1xx_hal_msp.c", "../Core/Src/stm32f1xx_it.c", "../Core/Src/stmflash.c", "../Core/Src/sys.c", "../Core/Src/system_stm32f1xx.c", "../Core/Src/usart.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_can.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c", "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c", "startup_stm32f103xb.s"], "alwaysInBuildSources": [], "sourceParams": {}, "options": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": true, "output-debug-info": "enable", "microcontroller-cpu": "cortex-m3", "microcontroller-fpu": "cortex-m3", "microcontroller-float": "cortex-m3", "$arch-extensions": "", "$clang-arch-extensions": "", "$armlink-arch-extensions": ""}, "c/cpp-compiler": {"optimization": "level-3", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "link-scatter": ["c:/Users/<USER>/Desktop/bootloader_hal/MDK-ARM/build/bootloader_hal/bootloader_hal.sct"]}}, "env": {"KEIL_OUTPUT_DIR": "bootloader_hal", "workspaceFolder": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "workspaceFolderBasename": "MDK-ARM", "OutDir": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM\\build\\bootloader_hal", "OutDirRoot": "build", "OutDirBase": "build\\bootloader_hal", "ProjectName": "bootloader_hal", "ConfigName": "bootloader_hal", "ProjectRoot": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM", "ExecutableName": "c:\\Users\\<USER>\\Desktop\\bootloader_hal\\MDK-ARM\\build\\bootloader_hal\\bootloader_hal", "ChipPackDir": "", "ChipName": "", "SYS_Platform": "win32", "SYS_DirSep": "\\", "SYS_DirSeparator": "\\", "SYS_PathSep": ";", "SYS_PathSeparator": ";", "SYS_EOL": "\r\n", "EIDE_BUILDER_DIR": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.14\\res\\tools\\win32\\unify_builder", "EIDE_BINARIES_VER": "12.1.1", "EIDE_MSYS": "C:\\Users\\<USER>\\.eide\\bin\\builder\\msys\\bin", "EIDE_PY3_CMD": "C:\\Users\\<USER>\\.eide\\bin\\python36\\python3.exe", "ToolchainRoot": "C:\\Keil_v5\\ARM\\ARMCC"}, "sysPaths": []}