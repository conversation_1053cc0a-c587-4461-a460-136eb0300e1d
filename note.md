# STM32 CAN Bootloader 修复记录

## 问题描述
- 小固件（4.7KB）可以正常运行，但大固件（37.7KB）无法运行
- 固件接收过程中缺少写入进度显示
- Flash 写入过程中可能存在数据丢失问题

## 主要修复内容

### 1. Flash 写入函数优化 (`iap.c`)

#### 问题：
- 原始的 `iap_write_block` 函数使用复杂的缓冲区逻辑，容易出错
- 缺少错误处理和返回值
- 地址计算可能有误

#### 修复：
```c
// 修改前：void iap_write_block(...)
// 修改后：uint8_t iap_write_block(...) 

uint8_t iap_write_block(uint32_t flash_addr, uint8_t *data, uint32_t len)
{
    HAL_StatusTypeDef status = HAL_OK;
    
    // 解锁Flash
    HAL_FLASH_Unlock();
    
    // 逐个半字写入，简化逻辑
    for (uint32_t i = 0; i < len; i += 2)
    {
        uint16_t data_to_write;
        
        if (i + 1 < len) {
            data_to_write = (uint16_t)data[i + 1] << 8 | (uint16_t)data[i];
        } else {
            data_to_write = (uint16_t)data[i];
        }
        
        status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_HALFWORD, 
                                 flash_addr + i, 
                                 data_to_write);
        
        if (status != HAL_OK) {
            HAL_FLASH_Lock();
            return 1; // 写入失败
        }
    }
    
    HAL_FLASH_Lock();
    return 0; // 写入成功
}
```

**关键改进：**
- 简化为逐字节写入，避免复杂的地址计算
- 添加错误处理和返回值
- 使用 HAL 库函数确保可靠性

### 2. 主循环中的 Flash 写入处理 (`main.c`)

#### 修复：
```c
// 处理Flash写入
if (flash_write_pending)
{
    printf("Writing 1KB to Flash at 0x%08X\r\n", current_flash_addr);
    
    // 执行Flash写入
    uint8_t write_result = iap_write_block(current_flash_addr, current_flash_buffer, FLASH_WRITE_SIZE);
    if (write_result == 0) {
        // 写入成功，更新地址和状态
        current_flash_addr += FLASH_WRITE_SIZE;
        
        // 计算并显示写入进度（写入成功后才计算）
        uint32_t written_bytes = current_flash_addr - FLASH_APP1_ADDR;
        uint32_t written_kb = written_bytes / 1024;
        uint32_t written_bytes_remainder = written_bytes % 1024;
        printf("Write successful! Written: %d bytes (%d.%03dKB), Total received: %d bytes\r\n", 
               written_bytes, written_kb, written_bytes_remainder, total_received_bytes);
    } else {
        printf("Flash write failed! Error code: %d\r\n", write_result);
        // 写入失败，重置状态
        reset_firmware_state();
    }
    
    flash_write_pending = 0;
}
```

**关键改进：**
- 添加写入结果检查
- 正确计算写入进度（基于实际写入的字节数）
- 写入失败时重置状态

### 3. CAN 接收缓冲区逻辑修复 (`canopen.c`)

#### 问题：
- 当缓冲区满时使用 `break` 跳出循环，导致同一 CAN 帧的剩余数据丢失

#### 修复：
```c
// 将数据写入当前接收缓冲区
for(uint8_t i = 0; i < RxMeg.DLC; i++)
{
    if(CAN1_RX_CNT < CAN_REC_LEN) {
        current_rx_buffer[CAN1_RX_CNT] = CAN_Rx_Data[i];
        CAN1_RX_CNT++;
        total_received_bytes++;
        
        // 缓冲区满了，需要切换缓冲区并标记写Flash
        if(CAN1_RX_CNT >= CAN_REC_LEN) {
            flash_write_pending = 1;
            CAN1_RX_CNT = 0;
            
            // 切换缓冲区
            if(current_rx_buffer == CAN_RX_BUF_A) {
                current_rx_buffer = CAN_RX_BUF_B;
                current_flash_buffer = CAN_RX_BUF_A;
            } else {
                current_rx_buffer = CAN_RX_BUF_A;
                current_flash_buffer = CAN_RX_BUF_B;
            }
            
            // 继续处理当前CAN帧的剩余字节
            // 移除了原来的 break 语句
        }
    }
}
```

**关键改进：**
- 移除了导致数据丢失的 `break` 语句
- 确保 CAN 帧的所有字节都被正确处理

### 4. 调试信息增强

#### 添加的调试功能：
```c
// CAN 接收调试信息（可选）
printf("CAN RX: ID=0x%03X, DLC=%d, Buffer=%s, Count=%d\r\n", 
       RxMeg.StdId, RxMeg.DLC, 
       (current_rx_buffer == CAN_RX_BUF_A) ? "A" : "B", 
       CAN1_RX_CNT);

// Flash 写入调试信息
printf("Debug: Writing %d bytes to 0x%08X\r\n", len, flash_addr);
printf("Debug: Successfully wrote %d bytes\r\n", len);

// 固件验证详细信息
printf("Stack pointer: 0x%08X\r\n", *(volatile uint32_t*)FLASH_APP1_ADDR);
printf("Reset vector: 0x%08X\r\n", *(volatile uint32_t*)(FLASH_APP1_ADDR + 4));
```

### 5. APP 跳转逻辑保持不变

**重要：** 保持了原始的 `iap_load_app` 函数不变，因为它已经能正常工作：

```c
void iap_load_app ( uint32_t appxaddr )
{
    if ( ( ( * ( volatile uint32_t* ) appxaddr ) & 0x2FFE0000 ) == 0x20000000 )
    {
        __set_MSP ( * ( volatile uint32_t* ) appxaddr );
        jump2app = ( iapfun ) * ( volatile uint32_t* ) ( appxaddr + 4 );
        MSR_MSP(*(volatile uint32_t*)appxaddr);
        jump2app();
    }
}
```

## 测试结果

### 修复前：
- 4.7KB 固件：可以运行，但无进度显示
- 37.7KB 固件：无法运行，疑似 Flash 写入错误

### 修复后：
- 4.7KB 固件：正常运行，显示完整进度
- 37.7KB 固件：正常运行，显示完整进度

### 典型输出示例：
```
Starting firmware reception...
Writing 1KB to Flash at 0x08005000
Debug: Writing 1024 bytes to 0x08005000
Debug: Successfully wrote 1024 bytes
Write successful! Written: 1024 bytes (1.000KB), Total received: 1040 bytes

Writing 1KB to Flash at 0x08005400
Debug: Writing 1024 bytes to 0x08005400
Debug: Successfully wrote 1024 bytes
Write successful! Written: 2048 bytes (2.000KB), Total received: 2064 bytes

...

Writing final 528 bytes to Flash
Debug: Writing 528 bytes to 0x08006000
Debug: Successfully wrote 528 bytes
Firmware reception completed! Total: 5152 bytes
Firmware validation successful!
Stack pointer: 0x20000460
Reset vector: 0x08005101
Firmware update completed! Send CAN command (ID:0x101, Data:0x01) to start APP.

Received jump to APP command!
Processing jump to APP command...
Starting user application via CAN command...
```

## 关键学习点

1. **保持工作代码不变**：如果某部分代码已经工作正常，应该避免不必要的修改
2. **逐步调试**：通过添加调试信息逐步定位问题
3. **数据流完整性**：确保 CAN 接收过程中没有数据丢失
4. **错误处理**：Flash 写入等关键操作必须有完善的错误处理
5. **进度反馈**：用户界面应该提供清晰的操作进度反馈

## 文件修改清单

- `Core/Src/iap.c` - Flash 写入函数优化
- `Core/Inc/iap.h` - 函数声明更新
- `Core/Src/main.c` - Flash 写入处理和进度显示
- `Core/Src/canopen.c` - CAN 接收逻辑修复

---
*修复完成日期：2025年8月15日*
*状态：测试通过，大小固件均可正常运行*
