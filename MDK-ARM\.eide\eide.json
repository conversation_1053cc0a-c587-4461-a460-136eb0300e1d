{"name": "bootloader_hal", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Application", "files": [], "folders": [{"name": "MDK-ARM", "files": [{"path": "startup_stm32f103xb.s"}], "folders": []}, {"name": "User", "files": [], "folders": [{"name": "Core", "files": [{"path": "../Core/Src/main.c"}, {"path": "../Core/Src/gpio.c"}, {"path": "../Core/Src/usart.c"}, {"path": "../Core/Src/stm32f1xx_it.c"}, {"path": "../Core/Src/stm32f1xx_hal_msp.c"}, {"path": "../Core/Src/can.c"}, {"path": "../Core/Src/canopen.c"}, {"path": "../Core/Src/iap.c"}, {"path": "../Core/Src/stmflash.c"}, {"path": "../Core/Src/sys.c"}, {"path": "../Core/Src/app_crc_verify.c"}], "folders": []}]}]}, {"name": "Drivers", "files": [], "folders": [{"name": "STM32F1xx_HAL_Driver", "files": [{"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_can.c"}], "folders": []}, {"name": "CMSIS", "files": [{"path": "../Core/Src/system_stm32f1xx.c"}], "folders": []}]}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "f69f235c5253c554db9bd3e129892125"}, "targets": {"bootloader_hal": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M3", "archExtensions": "", "floatingPointHardware": "none", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x5000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x4fff"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "OpenOCD", "uploadConfig": {"bin": "", "target": "stm32f1x", "interface": "cmsis-dap", "baseAddr": "0x08000000"}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}}, "custom_dep": {"name": "default", "incList": ["../Core/Inc", "../Drivers/STM32F1xx_HAL_Driver/Inc", "../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy", "../Drivers/CMSIS/Device/ST/STM32F1xx/Include", "../Drivers/CMSIS/Include", ".cmsis/include", "RTE/_bootloader_hal"], "libList": [], "defineList": ["USE_HAL_DRIVER", "STM32F103xB"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-3", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf"}}}}}, "version": "3.6"}